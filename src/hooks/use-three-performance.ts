"use client";

import { useEffect, useState } from "react";

interface PerformanceSettings {
  pixelRatio: number;
  antialias: boolean;
  shadows: boolean;
  particleCount: number;
  animationSpeed: number;
}

export function useThreePerformance(): PerformanceSettings {
  const [settings, setSettings] = useState<PerformanceSettings>({
    pixelRatio: 1,
    antialias: true,
    shadows: true,
    particleCount: 2000,
    animationSpeed: 1,
  });

  useEffect(() => {
    // Detect device capabilities
    const canvas = document.createElement("canvas");
    const gl = canvas.getContext("webgl") || canvas.getContext("experimental-webgl");
    
    if (!gl) {
      // Fallback for devices without WebGL
      setSettings({
        pixelRatio: 1,
        antialias: false,
        shadows: false,
        particleCount: 500,
        animationSpeed: 0.5,
      });
      return;
    }

    // Check for mobile devices
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );

    // Check for low-end devices
    const isLowEnd = navigator.hardwareConcurrency <= 2 || 
                     (navigator as any).deviceMemory <= 2;

    // Performance-based settings
    if (isMobile || isLowEnd) {
      setSettings({
        pixelRatio: Math.min(window.devicePixelRatio, 1.5),
        antialias: false,
        shadows: false,
        particleCount: 1000,
        animationSpeed: 0.7,
      });
    } else {
      setSettings({
        pixelRatio: Math.min(window.devicePixelRatio, 2),
        antialias: true,
        shadows: true,
        particleCount: 2000,
        animationSpeed: 1,
      });
    }

    // Cleanup
    canvas.remove();
  }, []);

  return settings;
}

// Hook for frame rate limiting
export function useFrameLimit(targetFPS: number = 60) {
  const [shouldRender, setShouldRender] = useState(true);
  
  useEffect(() => {
    const interval = 1000 / targetFPS;
    let lastTime = 0;
    
    const checkFrame = (currentTime: number) => {
      if (currentTime - lastTime >= interval) {
        setShouldRender(true);
        lastTime = currentTime;
      } else {
        setShouldRender(false);
      }
      requestAnimationFrame(checkFrame);
    };
    
    requestAnimationFrame(checkFrame);
  }, [targetFPS]);
  
  return shouldRender;
}

// Hook for visibility-based rendering
export function useVisibilityOptimization() {
  const [isVisible, setIsVisible] = useState(true);
  
  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsVisible(!document.hidden);
    };
    
    document.addEventListener("visibilitychange", handleVisibilityChange);
    
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);
  
  return isVisible;
}
