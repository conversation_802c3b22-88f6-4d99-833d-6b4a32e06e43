import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";
import { cn } from "@/lib/utils";
import { ThemeProvider } from "@/components/theme-provider";
import { AnimatedBackground } from "@/components/three/animated-background";
import { Suspense } from "react";

const inter = Inter({ 
  subsets: ["latin"],
  variable: "--font-inter",
});

const poppins = Poppins({ 
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-poppins",
});

export const viewport = {
  width: "device-width",
  initialScale: 1,
};

export const metadata: Metadata = {
  title: "S2P Infotech - Modern Business Solutions",
  description: "S2P Infotech provides cutting-edge technology solutions for modern businesses. Specializing in software development, digital transformation, and IT consulting.",
  keywords: "S2P Infotech, technology solutions, software development, IT consulting, digital transformation",
  authors: [{ name: "S2P Infotech" }],
  creator: "S2P Infotech",
  publisher: "S2P Infotech",
  icons: {
    icon: [
      { url: "/logo.png", sizes: "any", type: "image/png" },
      { url: "/logo.png", sizes: "16x16", type: "image/png" },
      { url: "/logo.png", sizes: "32x32", type: "image/png" },
    ],
    apple: [
      { url: "/logo.png", sizes: "180x180", type: "image/png" },
    ],
    shortcut: "/logo.png",
  },
  openGraph: {
    title: "S2P Infotech - Modern Business Solutions",
    description: "S2P Infotech provides cutting-edge technology solutions for modern businesses.",
    url: "https://s2pinfotech.com",
    siteName: "S2P Infotech",
    type: "website",
    images: [
      {
        url: "/logo.png",
        width: 1200,
        height: 630,
        alt: "S2P Infotech Logo",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "S2P Infotech - Modern Business Solutions",
    description: "S2P Infotech provides cutting-edge technology solutions for modern businesses.",
    images: ["/logo.png"],
  },
  robots: "index, follow",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body 
        className={cn(
          "min-h-screen bg-background font-sans antialiased",
          inter.variable,
          poppins.variable
        )}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <Suspense fallback={null}>
            <AnimatedBackground />
          </Suspense>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
