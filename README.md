# S2P Infotech - Portfolio Website

A modern, minimalistic portfolio website built with Next.js, TypeScript, and shadcn/ui components.

## Features

- 🌙 Dark theme by default with theme toggle
- 📱 Fully responsive design
- ⚡ Fast performance with Next.js 15
- 🎨 Beautiful UI with shadcn/ui components
- 🎭 Smooth animations with Framer Motion
- 📧 Contact form with validation
- 🔍 SEO optimized
- ♿ Accessibility focused

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Fonts**: Inter & Poppins (Google Fonts)

## Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Run the development server**:
   ```bash
   npm run dev
   ```

3. **Open your browser** and navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   ├── page.tsx          # Home page
│   ├── loading.tsx       # Loading UI
│   ├── error.tsx         # Error UI
│   ├── not-found.tsx     # 404 page
│   └── sitemap.ts        # SEO sitemap
├── components/
│   ├── layout/           # Layout components
│   │   ├── navbar.tsx
│   │   └── footer.tsx
│   ├── sections/         # Page sections
│   │   ├── hero-section.tsx
│   │   ├── services-section.tsx
│   │   ├── about-section.tsx
│   │   └── contact-section.tsx
│   ├── ui/              # shadcn/ui components
│   ├── theme-provider.tsx
│   └── theme-toggle.tsx
├── lib/
│   └── utils.ts         # Utility functions
└── hooks/               # Custom React hooks
```

## Sections

### Hero Section
- Company introduction
- Call-to-action buttons
- Key statistics
- Interactive elements

### Services Section
- Software Development
- Cloud Solutions
- Digital Transformation
- Data Analytics
- Cybersecurity
- IT Consulting

### About Section
- Company story
- Mission & Vision
- Core values
- Achievements

### Contact Section
- Contact form with validation
- Contact information
- Business hours
- Social links

## Customization

### Colors
The website uses CSS custom properties for theming. You can customize colors in `src/app/globals.css`:

```css
:root {
  --primary: 240 5.9% 10%;
  --secondary: 240 4.8% 95.9%;
  /* ... other colors */
}
```

### Content
Update company information in the respective section components:
- Hero: `src/components/sections/hero-section.tsx`
- Services: `src/components/sections/services-section.tsx`
- About: `src/components/sections/about-section.tsx`
- Contact: `src/components/sections/contact-section.tsx`

### Branding
- Replace logo: `public/logo.png`
- Update company name in components
- Modify metadata in `src/app/layout.tsx`

## Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms
The website can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Railway
- DigitalOcean App Platform

## Performance

The website is optimized for performance with:
- Static generation where possible
- Optimized images with Next.js Image component
- Minimal JavaScript bundle
- CSS-in-JS with zero runtime overhead
- Lazy loading of components

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## License

This project is licensed under the MIT License.

## Support

For support, email <EMAIL> or create an issue in the repository.
